# 文件传输SDK

一个安全、高效的Java文件传输解决方案，支持用户认证、断点续传和差异化限速，包含服务端SDK、客户端SDK和独立服务端应用。

## 项目特性

- ✅ **用户认证**: 基于HMAC-SHA256的安全认证机制
- ✅ **多用户隔离**: 每个用户独立的存储空间和配置
- ✅ **差异化限速**: 不同用户不同的传输速度配额
- ✅ **断点续传**: 支持文件上传下载中断后继续传输
- ✅ **分块传输**: 大文件自动分块，支持Range分块下载
- ✅ **秒传功能**: 基于MD5去重，避免重复上传
- ✅ **灵活下载**: 支持文件ID下载、相对路径下载和文件信息查询
- ✅ **进度监听**: 实时获取传输进度和状态
- ✅ **并发控制**: 支持多文件同时传输
- ✅ **文件校验**: MD5校验确保文件完整性
- ✅ **嵌入式数据库**: 使用SQLite，无需额外数据库配置
- ✅ **Spring Boot集成**: 支持自动配置和依赖注入
- ✅ **异步处理**: 专用线程池优化I/O性能
- ✅ **智能监控**: 自动监控传输状态和系统健康
- ✅ **管理接口**: 提供系统统计和管理功能
- ✅ **独立部署**: 提供可独立运行的服务端应用

## 项目结构

```
file-transfer-sdk/
├── pom.xml                           # 父项目配置
├── file-transfer-server-sdk/         # 服务端SDK
│   ├── src/main/java/com/sdesrd/filetransfer/server/
│   │   ├── controller/               # REST API控制器
│   │   ├── service/                  # 业务逻辑服务
│   │   ├── config/                   # 配置类和自动配置
│   │   ├── entity/                   # 数据库实体类
│   │   ├── mapper/                   # MyBatis映射器
│   │   ├── dto/                      # 数据传输对象
│   │   ├── util/                     # 工具类
│   │   └── interceptor/              # 拦截器
│   └── src/main/resources/
│       ├── sql/                      # SQLite数据库脚本
│       └── META-INF/spring.factories # Spring自动配置
├── file-transfer-client-sdk/         # 客户端SDK
│   └── src/main/java/com/sdesrd/filetransfer/client/
│       ├── config/                   # 客户端配置类
│       ├── dto/                      # 数据传输对象
│       ├── exception/                # 客户端异常定义
│       ├── listener/                 # 传输监听器接口
│       └── util/                     # 客户端工具类
├── file-transfer-client-demo/        # 客户端演示应用
│   └── src/main/java/com/sdesrd/filetransfer/demo/
│       ├── FileTransferClientDemo.java     # 主演示类
│       ├── DemoTransferListener.java       # 演示传输监听器
│       └── DemoStatistics.java            # 演示统计类
├── file-transfer-server-standalone/  # 独立服务端应用
│   └── src/main/java/com/sdesrd/filetransfer/server/standalone/
├── BUILD_AND_TEST_GUIDE.md          # 自动化构建和测试指南
├── integration-guide.md             # 集成指南
└── README.md                        # 项目说明
```

## 技术栈

| 组件 | 版本 | 说明 |
|------|------|------|
| **Java** | 8+ | 基础运行环境 |
| **Spring Boot** | 2.6.6 | 应用框架和自动配置 |
| **MyBatis Plus** | 3.5.6 | ORM框架和数据库操作 |
| **SQLite JDBC** | 3.42.0.0 | 嵌入式数据库驱动 |
| **OkHttp** | 4.9.3 | HTTP客户端库 |
| **Fastjson** | 1.2.78 | JSON序列化和反序列化 |
| **Hutool** | 5.3.8 | Java工具类库 |
| **Guava** | 29.0-jre | Google核心库（限流等） |
| **Druid** | 1.2.16 | 数据库连接池 |
| **Lombok** | 1.18.38 | 代码简化注解 |

## 快速开始

### 1. 环境要求

- **JDK**: 1.8 或更高版本
- **Maven**: 3.6.3 或更高版本
- **操作系统**: Windows/Linux/macOS

### 2. 自动化构建（推荐）

项目提供了完整的自动化构建和测试脚本 `build-and-test.sh`，具有以下特性：

**🔧 核心功能**
- **Java环境自动配置**: 自动查找并配置Java 8环境
- **Maven构建**: 完整的编译、安装流程
- **多层次测试**: 单元测试、集成测试、性能测试
- **覆盖率报告**: JaCoCo代码覆盖率分析
- **详细日志**: 完整的构建和测试日志记录

**📋 使用方法**

```bash
# Linux/macOS 系统 - 完整构建和测试流程（推荐）
./build-and-test.sh

# Windows 系统 - PowerShell脚本
.\build-and-test.ps1
  # 如果遇到权限问题，可使用 .\run-build-and-test.bat 引导启动

# 仅构建项目（不运行测试）
./build-and-test.sh --build-only

# 指定Java路径
./build-and-test.sh --java-home /path/to/java8

# 仅运行单元测试
./build-and-test.sh --unit-only

# 包含性能测试的完整流程
./build-and-test.sh --performance

# 显示详细执行过程
./build-and-test.sh --verbose

# 查看完整帮助信息
./build-and-test.sh --help
```

详细的自动化构建指南请参考：[BUILD_AND_TEST_GUIDE.md](BUILD_AND_TEST_GUIDE.md)

### 3. 手动编译

```bash
cd file-transfer-sdk
mvn clean install
```

### 4. 运行客户端演示应用

**启动服务端（必需）：**
```bash
cd file-transfer-server-standalone
mvn spring-boot:run
```

**运行客户端演示：**
```bash
cd file-transfer-client-demo
mvn exec:java -Dexec.mainClass="com.sdesrd.filetransfer.demo.FileTransferClientDemo"
```

或者运行自动化测试：
```bash
mvn exec:java -Dexec.mainClass="com.sdesrd.filetransfer.demo.FileTransferClientDemo" \
  -Ddemo.automated.test=true \
  -Ddemo.server.host=localhost \
  -Ddemo.server.port=49011
```

服务端访问地址：
- **健康检查**: http://localhost:49011/actuator/health
- **API文档**: http://localhost:49011/doc.html
- **服务端点**: http://localhost:49011/filetransfer/api/file/*

### 5. 运行独立服务端

```bash
cd file-transfer-server-standalone
mvn spring-boot:run
```

或者使用编译好的JAR包：

```bash
java -jar file-transfer-server-standalone/target/file-transfer-server-standalone-1.0.0.jar
```

## 使用指南

### 服务端SDK集成

#### 1. 添加依赖

```xml
<dependency>
    <groupId>com.sdesrd.filetransfer</groupId>
    <artifactId>file-transfer-server-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### 2. 配置文件

```yaml
# application.yml
spring:
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: jdbc:sqlite:./data/file-transfer/database.db

file:
  transfer:
    server:
      enabled: true
      database-path: ./data/file-transfer/database.db
      
      # 清理配置
      cleanup-interval: 3600000        # 1小时（毫秒）
      record-expire-time: 86400000     # 24小时（毫秒）
      
      # 默认配置（兜底配置）
      default:
        storage-path: ./data/file-transfer/files
        upload-rate-limit: 10485760  # 10MB/s
        download-rate-limit: 10485760  # 10MB/s
        default-chunk-size: 2097152  # 2MB
        max-file-size: 104857600  # 100MB
        max-in-memory-size: 10485760  # 10MB
        fast-upload-enabled: true
        rate-limit-enabled: true
      
      # 用户配置（必需）
      users:
        # 基础用户
        user1:
          secret-key: "user1-secret-2024"
          storage-path: ./data/users/user1
          upload-rate-limit: 2097152    # 2MB/s
          download-rate-limit: 2097152
          max-file-size: 52428800       # 50MB
          
        # VIP用户
        vip:
          secret-key: "vip-secret-2024"
          storage-path: ./data/users/vip
          upload-rate-limit: 10485760   # 10MB/s
          download-rate-limit: 10485760
          max-file-size: 209715200      # 200MB
          
        # 管理员
        admin:
          secret-key: "admin-secret-2024"
          storage-path: ./data/admin
          upload-rate-limit: 52428800   # 50MB/s
          download-rate-limit: 52428800
          max-file-size: 1073741824     # 1GB
          rate-limit-enabled: false     # 不限速
```

#### 3. 启动类

```java
@SpringBootApplication
public class YourApplication {
    public static void main(String[] args) {
        SpringApplication.run(YourApplication.class, args);
    }
}
```

### 客户端SDK使用

#### 1. 添加依赖

```xml
<dependency>
    <groupId>com.sdesrd.filetransfer</groupId>
    <artifactId>file-transfer-client-sdk</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### 2. 创建客户端

```java
// 配置客户端认证信息（必填）
ClientConfig config = new ClientConfig();
config.getAuth().setServerAddr("your-domain.com");
config.getAuth().setServerPort(49011);
config.getAuth().setUser("user1");  // 与服务端配置的用户名一致
config.getAuth().setSecretKey("user1-secret-2024");  // 与服务端对应用户的密钥一致

// 配置传输参数
config.setChunkSize(2 * 1024 * 1024); // 2MB 分块
config.setMaxConcurrentTransfers(3);

// 创建客户端
FileTransferClient client = new FileTransferClient(config);
```

#### 3. 上传文件

```java
// 创建传输监听器
TransferListener listener = new TransferListener() {
    @Override
    public void onProgress(TransferProgress progress) {
        System.out.printf("上传进度: %.2f%% (%s/%s)%n", 
            progress.getProgress(),
            FileUtils.formatFileSize(progress.getTransferredSize()),
            FileUtils.formatFileSize(progress.getTotalSize()));
    }
    
    @Override
    public void onCompleted(TransferProgress progress) {
        System.out.println("上传完成: " + progress.getFileName());
    }
};

// 异步上传文件
CompletableFuture<UploadResult> future = client.uploadFile(
    "/path/to/local/file.txt", 
    "remote-file.txt", 
    listener
);

// 等待上传完成
UploadResult result = future.get();
if (result.isSuccess()) {
    System.out.println("文件ID: " + result.getFileId());
}
```

#### 4. 下载文件

```java
// 获取文件信息
FileInfo fileInfo = client.getFileInfo("file-id");
System.out.printf("文件: %s, 大小: %s%n", 
    fileInfo.getFileName(), 
    fileInfo.getFormattedSize());

// 异步下载文件（按文件ID下载）
CompletableFuture<DownloadResult> future = client.downloadFile(
    "file-id", 
    "/path/to/save/file.txt", 
    listener
);

// 通过相对路径获取文件信息，然后下载
try {
    FileInfo pathFileInfo = client.getFileInfoByPath("docs/document.pdf", null);
    if (pathFileInfo != null) {
        // 使用获取到的fileId下载文件
        CompletableFuture<DownloadResult> future2 = client.downloadFile(
            pathFileInfo.getFileId(),
            "/path/to/save/document.pdf",
            listener);
        
        DownloadResult result = future2.get();
        if (result.isSuccess()) {
            System.out.println("下载成功: " + result.getLocalPath());
        }
    }
} catch (Exception e) {
    System.err.println("文件不存在或获取失败: " + e.getMessage());
}

// 便捷的相对路径下载方法
CompletableFuture<DownloadResult> future3 = client.downloadFileByPathViaFileId(
    "docs/document.pdf",
    "/path/to/save/document.pdf",
    listener
);

// 分块下载大文件（支持断点续传）
CompletableFuture<DownloadResult> future4 = client.downloadFileChunk(
    "file-id",
    "/path/to/save/large-file.zip",
    listener
);

// 等待下载完成
DownloadResult result = future.get();
if (result.isSuccess()) {
    System.out.println("保存路径: " + result.getLocalPath());
}
```

#### 5. 查询传输进度

```java
TransferProgress progress = client.queryProgress("transfer-id");
System.out.printf("进度: %.2f%%, 状态: %s%n", 
    progress.getProgress(), 
    progress.isCompleted() ? "完成" : "传输中");
```

## API接口

### 文件传输接口

所有传输接口都需要在HTTP请求头中提供认证信息：
- `X-File-Transfer-User`: 用户名
- `X-File-Transfer-Auth`: 认证令牌（Base64编码的时间戳和HMAC-SHA256签名）

| 方法 | 路径 | 功能 | 认证 |
|------|------|------|------|
| `POST` | `/filetransfer/api/file/upload/init` | 初始化文件上传 | 🔒 必需 |
| `POST` | `/filetransfer/api/file/upload/chunk` | 上传文件分块 | 🔒 必需 |
| `POST` | `/filetransfer/api/file/upload/complete/{transferId}` | 完成文件上传 | 🔒 必需 |
| `GET` | `/filetransfer/api/file/download/{fileId}` | 下载文件（支持断点续传） | 🔒 必需 |
| `GET` | `/filetransfer/api/file/download/info/{fileId}` | 获取文件下载信息 | 🔒 必需 |
| `GET` | `/filetransfer/api/file/download/chunk/{fileId}` | 分块下载文件（支持Range） | 🔒 必需 |
| `GET` | `/filetransfer/api/file/file/info/by-path` | 通过相对路径获取文件信息 | 🔒 必需 |
| `GET` | `/filetransfer/api/file/progress/{transferId}` | 查询传输进度 | 🔒 必需 |
| `GET` | `/filetransfer/api/file/health` | 健康检查 | ✅ 无需 |

### 管理接口

用于系统监控和管理的接口（生产环境建议通过网络层面限制访问）：

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| `GET` | `/filetransfer/api/admin/statistics` | 获取传输统计信息 | 返回各状态传输数量和成功率 |
| `GET` | `/filetransfer/api/admin/health` | 系统健康检查（详细） | 包含JVM信息和传输统计 |
| `GET` | `/filetransfer/api/admin/clear-rate-limiters` | 清理限流器缓存 | 清理所有用户的限流器状态 |
| `POST` | `/filetransfer/api/admin/rebuild-database` | 重建SQLite数据库 | 扫描文件系统重建数据库记录 |

### API响应格式

统一的API响应格式：
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "code": 200
}
```

文件信息查询API响应示例：
```json
{
  "success": true,
  "message": "获取文件信息成功",
  "data": {
    "fileId": "abc123def456",
    "fileName": "document.pdf",
    "fileSize": 1048576,
    "relativePath": "docs/document.pdf",
    "fileType": "application/pdf",
    "uploadTime": "2024-01-15 10:30:00",
    "lastModified": 1705294200000,
    "canRead": true,
    "canWrite": false,
    "formattedSize": "1.0MB"
  }
}
```

## 独立服务端应用

### 概述

`file-transfer-server-standalone` 是一个可独立运行的服务端应用，包含完整的文件传输功能，无需集成到其他Spring Boot项目中。

### 特性

- ✅ **独立运行**: 可作为独立进程运行
- ✅ **完整功能**: 包含所有文件传输SDK的功能
- ✅ **监控端点**: 内置Spring Boot Actuator健康检查
- ✅ **配置灵活**: 支持外部配置文件和环境变量
- ✅ **生产就绪**: 支持Docker部署和系统服务配置

### 使用方法

#### 1. 编译独立应用

```bash
cd file-transfer-server-standalone
mvn clean package
```

#### 2. 运行独立应用

```bash
# 使用默认配置运行
java -jar target/file-transfer-server-standalone-1.0.0.jar

# 使用自定义配置文件
java -jar target/file-transfer-server-standalone-1.0.0.jar \
  --spring.config.location=file:./config/application.yml

# 使用环境变量配置
export SERVER_PORT=9090
export FILE_TRANSFER_SERVER_STORAGE_PATH=/data/files
java -jar target/file-transfer-server-standalone-1.0.0.jar
```

#### 3. Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
WORKDIR /app
COPY target/file-transfer-server-standalone-1.0.0.jar app.jar
EXPOSE 49011
VOLUME ["/data"]
CMD ["java", "-jar", "app.jar"]
```

```bash
docker build -t file-transfer-server .
docker run -d -p 49011:49011 -v /host/data:/data file-transfer-server
```

#### 4. 系统服务部署

创建systemd服务文件 `/etc/systemd/system/file-transfer-server.service`：

```ini
[Unit]
Description=File Transfer Server
After=network.target

[Service]
Type=simple
User=filetransfer
ExecStart=/usr/bin/java -jar /opt/file-transfer/file-transfer-server-standalone-1.0.0.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 监控端点

独立服务端提供以下监控端点：

- **健康检查**: http://localhost:49011/actuator/health
- **系统信息**: http://localhost:49011/actuator/info
- **性能指标**: http://localhost:49011/actuator/metrics

## 认证机制

- **认证方式**: HMAC-SHA256签名
- **令牌格式**: Base64(timestamp:signature)
- **签名数据**: username:timestamp
- **有效期**: 5分钟
- **防重放**: 基于时间戳

## 数据库设计

### 文件传输记录表 (file_transfer_record)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键ID |
| file_id | TEXT | 文件标识符(MD5) |
| file_name | TEXT | 原始文件名 |
| file_size | INTEGER | 文件大小 |
| file_path | TEXT | 文件存储路径 |
| transferred_size | INTEGER | 已传输大小 |
| total_chunks | INTEGER | 分块总数 |
| completed_chunks | INTEGER | 已完成分块数 |
| status | INTEGER | 传输状态(0-待传输,1-传输中,2-完成,3-失败) |
| client_ip | TEXT | 客户端IP |
| create_time | TEXT | 创建时间 |
| update_time | TEXT | 更新时间 |
| complete_time | TEXT | 完成时间 |

### 文件分块记录表 (file_chunk_record)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | TEXT | 主键ID |
| transfer_id | TEXT | 传输记录ID |
| chunk_index | INTEGER | 分块序号 |
| chunk_size | INTEGER | 分块大小 |
| chunk_path | TEXT | 分块文件路径 |
| chunk_md5 | TEXT | 分块MD5值 |
| status | INTEGER | 分块状态 |
| create_time | TEXT | 创建时间 |
| update_time | TEXT | 更新时间 |

## 配置参数

### 服务端配置

#### 全局配置
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `file.transfer.server.enabled` | 是否启用服务 | `true` |
| `file.transfer.server.database-path` | 数据库文件路径 | `./data/file-transfer/database.db` |
| `file.transfer.server.cleanup-interval` | 清理间隔（毫秒） | `3600000` (1小时) |
| `file.transfer.server.record-expire-time` | 记录过期时间（毫秒） | `86400000` (24小时) |

#### 默认配置 (default节点)
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `storage-path` | 默认文件存储路径 | `./data/file-transfer/files` |
| `upload-rate-limit` | 默认上传限速(字节/秒) | `10485760` (10MB/s) |
| `download-rate-limit` | 默认下载限速(字节/秒) | `10485760` (10MB/s) |
| `default-chunk-size` | 默认分块大小 | `2097152` (2MB) |
| `max-file-size` | 默认最大文件大小 | `104857600` (100MB) |
| `max-in-memory-size` | 默认最大内存大小 | `10485760` (10MB) |
| `fast-upload-enabled` | 是否启用秒传 | `true` |
| `rate-limit-enabled` | 是否启用限速 | `true` |

#### 用户配置 (users节点)
| 参数 | 说明 | 是否必填 |
|------|------|--------|
| `secret-key` | 用户密钥 | ✅ 必填 |
| `storage-path` | 用户存储路径 | 可选 |
| `upload-rate-limit` | 用户上传限速 | 可选 |
| `download-rate-limit` | 用户下载限速 | 可选 |
| `default-chunk-size` | 用户分块大小 | 可选 |
| `max-file-size` | 用户最大文件大小 | 可选 |
| `max-in-memory-size` | 用户最大内存大小 | 可选 |
| `fast-upload-enabled` | 用户是否启用秒传 | 可选 |
| `rate-limit-enabled` | 用户是否启用限速 | 可选 |

### 客户端配置

#### 认证配置 (必填)
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `auth.serverAddr` | 服务器地址 | `localhost` |
| `auth.serverPort` | 服务器端口 | `49011` |
| `auth.user` | 用户名 | ❌ 必填 |
| `auth.secretKey` | 用户密钥 | ❌ 必填 |

#### 传输配置 (可选)
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `chunkSize` | 分块大小 | `2097152` (2MB) |
| `maxConcurrentTransfers` | 最大并发传输数 | `3` |
| `connectTimeoutSeconds` | 连接超时时间 | `30` |
| `readTimeoutSeconds` | 读取超时时间 | `60` |
| `retryCount` | 重试次数 | `3` |

## 测试和质量保证

### 自动化测试

项目包含完整的自动化测试套件：

- **单元测试**: 各组件的独立功能测试
- **集成测试**: 组件间协作和端到端测试
- **性能测试**: 系统性能指标验证

### 代码覆盖率

使用JaCoCo生成测试覆盖率报告：

```bash
# 运行测试并生成覆盖率报告
./build-and-test.sh

# 跳过覆盖率报告生成
./build-and-test.sh --skip-coverage

# 查看覆盖率报告
open logs/coverage/*/index.html
```

### 质量检查

```bash
# 运行完整的质量检查流程（包含性能测试）
./build-and-test.sh --performance

# 仅运行集成测试
./build-and-test.sh --integration-only
```

## 部署指南

### 生产环境部署

#### 1. 安全配置

```yaml
# 生产环境配置建议
file:
  transfer:
    server:
      # 设置合理的过期时间
      cleanup-interval: 1800000     # 30分钟清理一次
      record-expire-time: 259200000 # 3天过期
      
      users:
        # 使用强密钥
        prod-user:
          secret-key: "prod-very-long-and-complex-secret-key-2024"
          # 其他配置...
```

#### 2. 性能调优

```yaml
# 根据服务器配置调整
file:
  transfer:
    server:
      default:
        default-chunk-size: 4194304  # 4MB（高带宽环境）
        # 或
        default-chunk-size: 1048576  # 1MB（低带宽环境）
```

#### 3. 监控配置

```yaml
# 日志配置
logging:
  level:
    com.sdesrd.filetransfer: INFO  # 生产环境使用INFO级别
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./logs/file-transfer.log
    max-size: 100MB
    max-history: 30
```

### Docker部署

参见[独立服务端应用](#独立服务端应用)部分的Docker部署说明。

## 最佳实践

### 1. 项目集成

在现有Spring Boot项目中集成服务端SDK：

```java
@SpringBootApplication
@ComponentScan(basePackages = {"com.your.package", "com.sdesrd.filetransfer.server"})
public class YourApplication {
    // ...
}
```

### 2. 自定义用户配置

```yaml
file:
  transfer:
    server:
      users:
        # 试用用户 - 严格限制
        trial:
          secret-key: "trial-secret-2024"
          storage-path: ./data/trial
          upload-rate-limit: 524288      # 512KB/s
          max-file-size: 5242880         # 5MB
          
        # 企业用户 - 高级配置
        enterprise:
          secret-key: "enterprise-secret-2024"
          storage-path: ./data/enterprise
          upload-rate-limit: 20971520    # 20MB/s
          max-file-size: 1073741824      # 1GB
```

### 3. 错误处理

```java
try {
    UploadResult result = client.uploadFileSync(filePath, null, null);
    if (!result.isSuccess()) {
        log.error("上传失败: {}", result.getErrorMessage());
    }
} catch (FileTransferException e) {
    log.error("文件传输异常", e);
} catch (Exception e) {
    log.error("其他异常", e);
}
```

### 4. 文件下载策略

```java
// 下载前先获取文件信息
FileInfo fileInfo = client.getFileInfo("file-id");
if (fileInfo.getFileSize() > 50 * 1024 * 1024) { // 大于50MB
    // 使用分块下载
    client.downloadFileChunk("file-id", localPath, 0, -1, listener);
} else {
    // 使用普通下载
    client.downloadFile("file-id", localPath, listener);
}

// 相对路径下载（适用于已知文件结构场景）
client.downloadFileByPathViaFileId("docs/user-manual.pdf", localPath, listener);
```

### 5. 资源管理

```java
// 使用try-with-resources确保资源释放
try (FileTransferClient client = new FileTransferClient(config)) {
    // 执行文件传输操作
    client.uploadFileSync(filePath, null, listener);
}
```

## 故障排除

### 常见问题

#### Q: 如何配置用户认证？
A: 在服务端配置文件的 `users` 节点下添加用户，设置 `secret-key`。客户端使用对应的用户名和密钥进行认证。

#### Q: 如何为不同用户设置不同的传输限速？
A: 在用户配置中分别设置 `upload-rate-limit` 和 `download-rate-limit`。未配置的用户将使用默认配置。

#### Q: 客户端认证失败怎么办？
A: 检查以下几点：
- 用户名是否在服务端配置中存在
- 密钥是否与服务端配置一致
- 网络连接是否正常
- 认证令牌是否过期（有效期5分钟）

#### Q: 传输失败率过高怎么办？
A: 
1. 检查网络连接稳定性
2. 调整重试次数和超时时间
3. 适当减小分块大小
4. 检查服务端日志确认具体错误原因

#### Q: 如何监控系统性能？
A: 
1. 使用 `/filetransfer/api/admin/health` 查看系统状态
2. 通过自动化测试脚本监控性能
3. 监控JVM内存使用情况
4. 定期检查磁盘空间

#### Q: 数据库表是否自动创建？
A: 是的，应用启动时会自动检查并创建必要的表结构。

#### Q: 相对路径下载时提示路径不安全怎么办？
A: 服务端会自动验证路径安全性，确保路径不包含".."、"../"等试图访问上级目录的危险操作。请检查路径格式，使用相对于用户存储根目录的正确路径。

#### Q: 如何选择合适的下载方式？
A: 
1. **小文件（<50MB）**: 使用普通下载 `downloadFile()`
2. **大文件（>50MB）**: 使用分块下载 `downloadFileChunk()` 支持断点续传
3. **已知相对路径**: 先使用 `getFileInfoByPath()` 获取文件信息，再通过fileId下载
4. **便捷相对路径下载**: 使用 `downloadFileByPathViaFileId()` 一步完成
5. **下载前预览**: 先调用 `getFileInfo()` 获取文件信息

#### Q: 数据库损坏了怎么办？
A: 系统具备容错机制：
1. **自动容错**: 如果数据库查询失败，系统会自动通过文件路径规则查找物理文件
2. **手动重建**: 调用 `/filetransfer/api/admin/rebuild-database` 接口重建数据库，操作前会自动备份
3. **文件路径规则**: 文件按 `${storage-path}/fileId前4位/fileId/文件名` 规则存储

#### Q: 为什么改变了文件存储规则？
A: 
1. **统一管理**: 所有用户共享统一存储路径，便于管理和维护
2. **容错增强**: 基于fileId的路径规则使系统能够在数据库故障时自动恢复
3. **性能优化**: 减少路径复杂度，提高文件查找效率
4. **扩展性**: 更容易实现分布式存储和集群部署

#### Q: 如何通过相对路径获取文件？
A: 使用新的API方式：
```java
// 获取文件信息
FileInfo fileInfo = client.getFileInfoByPath("docs/file.pdf", null);
if (fileInfo != null) {
    // 使用fileId下载
    client.downloadFile(fileInfo.getFileId(), localPath, listener);
}

// 或使用便捷方法
client.downloadFileByPathViaFileId("docs/file.pdf", localPath, listener);
```

## 注意事项

1. **用户认证**: 所有操作都需要提供有效的用户名和密钥，缺少认证信息会返回401错误
2. **密钥安全**: 密钥泄露会导致安全风险，请妥善保管并定期更换
3. **文件存储规则**: 文件按照 `${storage-path}/fileId前4位/fileId/实体文件名` 的规则存储，所有用户共享统一的存储路径
4. **数据库容错**: 系统具备容错机制，如果SQLite数据库损坏或查询失败，会尝试通过文件路径规则查找物理文件
5. **数据库重建**: 提供 `/filetransfer/api/admin/rebuild-database` 接口，可扫描存储目录重建数据库记录，操作前会自动备份原数据库
6. **文件大小限制**: 不同用户可以有不同的文件大小限制，可通过配置调整
7. **传输限速**: 每个用户有独立的速度限制，超出限制会被限流
8. **数据库位置**: SQLite文件位置需要有读写权限
9. **并发限制**: 合理设置并发数，避免资源耗尽
10. **认证令牌**: 令牌有效期5分钟，过期需要重新生成
11. **错误重试**: 网络不稳定时适当增加重试次数
12. **日志级别**: 生产环境建议调整为INFO级别
13. **管理接口安全**: 生产环境应通过网络层面限制 `/filetransfer/api/admin/*` 接口访问
14. **监控告警**: 建议接入外部监控系统，及时发现异常
15. **磁盘空间**: 定期清理过期文件，避免磁盘空间耗尽
16. **路径安全**: 相对路径查询时会自动验证路径安全性，禁止包含".."等危险路径操作
17. **性能调优**: 根据实际硬件配置调整线程池大小和分块大小
18. **数据库备份**: 使用数据库重建功能前，系统会自动备份当前数据库文件
19. **文件路径变更**: 现有的多用户分离存储已改为统一存储，便于管理和容错

## 🚀 新增功能特性

### 增强的重试机制

SDK现在支持智能重试机制，包括：

- **指数退避策略**: 重试间隔逐渐增加，避免系统过载
- **抖动算法**: 防止多个客户端同时重试造成的雪崩效应
- **可配置异常类型**: 只对特定类型的异常进行重试
- **超时控制**: 支持重试超时设置

```java
// 使用增强的重试机制
ClientConfig config = ClientConfigBuilder.create()
    .serverAddr("localhost")
    .serverPort(49011)
    .auth("user", "secret-key")
    .retry(5, 1000) // 最多重试5次，基础延迟1秒
    .build();

FileTransferClient client = new FileTransferClient(config);
```

### 智能并发控制

新增的并发传输管理器提供：

- **动态槽位管理**: 智能分配传输资源
- **传输统计**: 实时监控传输状态和性能
- **任务取消**: 支持取消正在进行的传输任务
- **资源隔离**: 上传和下载任务独立管理

```java
// 获取传输统计
ConcurrentTransferManager.TransferStats stats = client.getTransferStats();
System.out.println("活跃传输: " + stats.getActiveTransfers());
System.out.println("总上传字节: " + FileUtils.formatFileSize(stats.getTotalUploadedBytes()));

// 检查可用槽位
if (client.hasAvailableSlot()) {
    // 启动新的传输任务
}

// 取消传输任务
client.cancelTransfer("task-id");
```

### 增强的分片下载

多线程分片下载功能包括：

- **智能分片**: 根据文件大小自动计算最优分片数量
- **并发下载**: 多线程同时下载不同分片
- **断点续传**: 智能检测已下载部分，从断点继续
- **完整性验证**: 下载完成后自动验证文件完整性

```java
// 使用增强的分片下载
CompletableFuture<DownloadResult> future = client.downloadFileChunk(
    fileId,
    savePath,
    new TransferListener() {
        @Override
        public void onProgress(TransferProgress progress) {
            System.out.printf("下载进度: %.1f%% - 速度: %s/s\n",
                progress.getProgress(),
                FileUtils.formatFileSize(progress.getSpeed()));
        }
    }
);
```

### 性能监控系统

服务端新增性能监控功能：

- **实时指标**: JVM内存、CPU、线程数等系统指标
- **传输统计**: 上传/下载次数、字节数、成功率等
- **历史记录**: 保留性能指标历史数据
- **自动清理**: 定期清理过期的性能数据

```java
// 启动性能监控
@PostConstruct
public void initPerformanceMonitoring() {
    PerformanceMonitor.startMonitoring();
}

// 获取性能指标
PerformanceMonitor.PerformanceMetrics metrics = PerformanceMonitor.getCurrentMetrics();
System.out.println("堆内存使用率: " + metrics.getHeapMemoryUsagePercent() + "%");

// 获取传输统计
PerformanceMonitor.TransferStatistics stats = PerformanceMonitor.getTransferStatistics();
System.out.println("传输成功率: " + stats.getSuccessRate() + "%");
```

### 增强的限流控制

优化的限流机制提供：

- **精细化控制**: 支持用户级别的独立限速
- **动态调整**: 运行时动态修改限速配置
- **超时机制**: 支持限流超时设置
- **资源管理**: 自动清理空闲的限流器

```java
// 应用带超时的限流
boolean success = RateLimitUtils.applyRateLimitWithTimeout(
    "user1_upload",
    1024 * 1024, // 1MB/s
    1024,        // 1KB数据
    5000         // 5秒超时
);

// 获取限流器统计
String stats = RateLimitUtils.getRateLimiterStats();
System.out.println(stats);

// 清理用户限流器
RateLimitUtils.clearUserRateLimiters("user1");
```

### 演示应用

新增的演示应用展示所有增强功能：

```bash
# 运行增强功能演示
cd file-transfer-client-demo
mvn exec:java -Dexec.mainClass="com.sdesrd.filetransfer.demo.EnhancedFileTransferDemo"
```

演示功能包括：
- 单文件上传演示
- 并发上传演示
- 增强分片下载演示
- 断点续传演示
- 传输统计查看
- 性能压力测试
- 重试机制演示

## 相关文档

- [自动化构建和测试指南](BUILD_AND_TEST_GUIDE.md) - 详细的构建、测试和CI/CD流程
- [集成指南](integration-guide.md) - 项目集成和部署指南
- [独立服务端README](file-transfer-server-standalone/README.md) - 独立服务端详细说明
