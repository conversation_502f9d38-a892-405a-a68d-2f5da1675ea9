import java.io.IOException;
import okhttp3.*;

public class TestUrlFix {
    public static void main(String[] args) {
        // 测试正确的URL
        String correctUrl = "http://localhost:49011/filetransfer/api/file/health";
        String wrongUrl = "http://localhost:49011/file-transfer/api/file/health";
        
        OkHttpClient client = new OkHttpClient();
        
        System.out.println("测试正确的URL: " + correctUrl);
        testUrl(client, correctUrl);
        
        System.out.println("\n测试错误的URL: " + wrongUrl);
        testUrl(client, wrongUrl);
    }
    
    private static void testUrl(OkHttpClient client, String url) {
        Request request = new Request.Builder()
                .url(url)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            System.out.println("状态码: " + response.code());
            System.out.println("响应: " + response.body().string());
        } catch (IOException e) {
            System.out.println("请求失败: " + e.getMessage());
        }
    }
}
