# JaCoCo测试覆盖率配置指南

## 🎯 概述

本指南说明如何为文件传输SDK项目配置JaCoCo测试覆盖率报告。

## ✅ 当前状态

- ✅ 测试脚本已修复，不再因为缺少JaCoCo配置而报错
- ✅ 测试脚本会自动检测JaCoCo配置，如果没有配置则跳过覆盖率报告生成
- ✅ 所有单元测试正常运行，通过率100%

## 🛠️ 如何添加JaCoCo配置

如果您希望生成测试覆盖率报告，请按以下步骤配置：

### 1. 在父POM中添加版本属性

在 `pom.xml` 的 `<properties>` 部分添加：

```xml
<properties>
    <!-- 现有属性... -->
    <maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
    <jacoco-maven-plugin.version>0.8.7</jacoco-maven-plugin.version>
</properties>
```

### 2. 在pluginManagement中添加插件管理

在 `<pluginManagement><plugins>` 部分添加：

```xml
<!-- Maven Surefire Plugin -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <version>${maven-surefire-plugin.version}</version>
</plugin>

<!-- JaCoCo Maven Plugin -->
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>${jacoco-maven-plugin.version}</version>
</plugin>
```

### 3. 在build/plugins中添加执行配置

在 `<build><plugins>` 部分添加：

```xml
<!-- JaCoCo测试覆盖率插件 -->
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <executions>
        <!-- 准备JaCoCo代理 -->
        <execution>
            <id>prepare-agent</id>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <!-- 生成测试覆盖率报告 -->
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
        <!-- 聚合报告 -->
        <execution>
            <id>report-aggregate</id>
            <phase>verify</phase>
            <goals>
                <goal>report-aggregate</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

## 🚀 使用方法

配置完成后，您可以：

### 生成覆盖率报告
```bash
# 运行测试并生成覆盖率报告
./test.sh --unit-only

# 或者手动生成
mvn clean test jacoco:report
```

### 查看覆盖率报告
报告将生成在以下位置：
- `./logs/coverage/file-transfer-server-sdk/index.html`
- `./logs/coverage/file-transfer-client-sdk/index.html`
- `./logs/coverage/file-transfer-demo/index.html`

### 跳过覆盖率报告
如果不需要覆盖率报告，可以使用：
```bash
./test.sh --unit-only --skip-coverage
```

## 📊 覆盖率报告功能

配置JaCoCo后，您将获得：

1. **行覆盖率** - 显示哪些代码行被测试覆盖
2. **分支覆盖率** - 显示条件分支的覆盖情况
3. **方法覆盖率** - 显示方法级别的覆盖率
4. **类覆盖率** - 显示类级别的覆盖率
5. **HTML报告** - 可视化的覆盖率报告
6. **聚合报告** - 整个项目的综合覆盖率

## 🔧 故障排除

### 问题：Maven报告插件版本冲突
**解决方案：** 确保使用兼容的插件版本，建议使用上述指定的版本。

### 问题：覆盖率数据不准确
**解决方案：** 确保在运行测试前清理项目：
```bash
mvn clean test jacoco:report
```

### 问题：聚合报告生成失败
**解决方案：** 确保所有子模块都正确配置了JaCoCo插件。

## 📝 注意事项

1. **Java 8兼容性** - 使用的JaCoCo版本0.8.7完全支持Java 8
2. **性能影响** - JaCoCo会轻微影响测试执行速度，但影响很小
3. **报告大小** - 覆盖率报告可能会占用一定磁盘空间
4. **CI/CD集成** - 可以将覆盖率报告集成到CI/CD流水线中

## 🎉 总结

当前的测试脚本修复确保了：
- ✅ 无论是否配置JaCoCo，测试都能正常运行
- ✅ 智能检测JaCoCo配置，自动决定是否生成覆盖率报告
- ✅ 提供清晰的日志信息，指导用户如何配置JaCoCo
- ✅ 保持向后兼容性，不影响现有的测试流程

如果您需要覆盖率报告功能，请按照本指南进行配置。如果不需要，当前的测试脚本已经完全满足需求。
