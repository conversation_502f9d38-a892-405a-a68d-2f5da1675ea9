<?xml version="1.0" encoding="UTF-8"?>
<!-- JaCoCo专用Maven设置文件 -->
<!-- 用途：解决JaCoCo报告生成中的JCE安全策略和网络连接问题 -->
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
                              http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库配置 -->
    <localRepository>${user.home}/.m2/repository</localRepository>

    <!-- 离线模式配置 -->
    <offline>false</offline>

    <!-- 插件组配置 -->
    <pluginGroups>
        <pluginGroup>org.jacoco</pluginGroup>
        <pluginGroup>org.apache.maven.plugins</pluginGroup>
    </pluginGroups>

    <!-- 代理配置（如果需要） -->
    <proxies>
        <!-- 如果在企业网络环境中，可以在这里配置代理 -->
    </proxies>

    <!-- 服务器配置 -->
    <servers>
        <!-- 如果需要访问私有仓库，可以在这里配置认证信息 -->
    </servers>

    <!-- 镜像配置 - 使用阿里云镜像加速下载 -->
    <mirrors>
        <mirror>
            <id>aliyun-central</id>
            <mirrorOf>central</mirrorOf>
            <name>阿里云中央仓库</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </mirror>
        <mirror>
            <id>aliyun-public</id>
            <mirrorOf>*</mirrorOf>
            <name>阿里云公共仓库</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </mirror>
    </mirrors>

    <!-- 配置文件 -->
    <profiles>
        <!-- JaCoCo专用配置 -->
        <profile>
            <id>jacoco-profile</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            
            <properties>
                <!-- Maven编译配置 -->
                <maven.compiler.source>8</maven.compiler.source>
                <maven.compiler.target>8</maven.compiler.target>
                <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
                
                <!-- JaCoCo配置 -->
                <jacoco.version>0.8.11</jacoco.version>
                <jacoco.skip>false</jacoco.skip>
                
                <!-- 网络配置 - 解决JCE安全策略问题 -->
                <maven.wagon.http.ssl.insecure>true</maven.wagon.http.ssl.insecure>
                <maven.wagon.http.ssl.allowall>true</maven.wagon.http.ssl.allowall>
                <maven.wagon.http.ssl.ignore.validity.dates>true</maven.wagon.http.ssl.ignore.validity.dates>
                
                <!-- 连接超时配置 -->
                <maven.wagon.http.connectionTimeout>30000</maven.wagon.http.connectionTimeout>
                <maven.wagon.http.readTimeout>30000</maven.wagon.http.readTimeout>
                
                <!-- 重试配置 -->
                <maven.wagon.http.retryHandler.count>3</maven.wagon.http.retryHandler.count>
                
                <!-- JVM配置 -->
                <argLine>-Djava.security.policy=all.policy -Djava.security.egd=file:/dev/./urandom</argLine>
            </properties>
            
            <repositories>
                <repository>
                    <id>central</id>
                    <name>Maven Central Repository</name>
                    <url>https://maven.aliyun.com/repository/central</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            
            <pluginRepositories>
                <pluginRepository>
                    <id>central</id>
                    <name>Maven Plugin Repository</name>
                    <url>https://maven.aliyun.com/repository/central</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <releases>
                        <updatePolicy>never</updatePolicy>
                    </releases>
                </pluginRepository>
            </pluginRepositories>
        </profile>
        
        <!-- Java 8专用配置 -->
        <profile>
            <id>java8-profile</id>
            <activation>
                <jdk>1.8</jdk>
            </activation>
            <properties>
                <!-- Java 8特定配置 -->
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                
                <!-- JaCoCo Java 8兼容配置 -->
                <jacoco.agent.argLine>-javaagent:${settings.localRepository}/org/jacoco/org.jacoco.agent/0.8.11/org.jacoco.agent-0.8.11-runtime.jar=destfile=${project.build.directory}/jacoco.exec</jacoco.agent.argLine>
            </properties>
        </profile>
        
        <!-- Java 11+兼容配置 -->
        <profile>
            <id>java11plus-profile</id>
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <properties>
                <!-- Java 11+模块系统兼容配置 -->
                <argLine>
                    --add-opens=java.base/java.lang=ALL-UNNAMED
                    --add-opens=java.base/java.security=ALL-UNNAMED
                    --add-opens=java.base/javax.crypto=ALL-UNNAMED
                    --illegal-access=permit
                    -Djava.security.policy=all.policy
                    -Djava.security.egd=file:/dev/./urandom
                </argLine>
            </properties>
        </profile>
    </profiles>

    <!-- 激活的配置文件 -->
    <activeProfiles>
        <activeProfile>jacoco-profile</activeProfile>
    </activeProfiles>

</settings>
