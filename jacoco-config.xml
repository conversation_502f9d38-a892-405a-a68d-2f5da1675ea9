<!-- JaCoCo插件版本配置 -->
<!-- 重要：此项目必须使用Java 8环境 (~/.jdks/corretto-1.8.0_452) -->
<!-- JaCoCo报告生成失败的根本原因是使用了Java 21而不是Java 8 -->
<maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
<jacoco-maven-plugin.version>0.8.11</jacoco-maven-plugin.version>

<!-- Java 8环境路径配置 -->
<java8.home>${user.home}/.jdks/corretto-1.8.0_452</java8.home>

<!-- JaCoCo插件管理配置 -->
<!-- Maven Surefire Plugin -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <version>${maven-surefire-plugin.version}</version>
</plugin>

<!-- JaCoCo Maven Plugin -->
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>${jacoco-maven-plugin.version}</version>
</plugin>

<!-- JaCoCo插件执行配置 -->
<!-- JaCoCo测试覆盖率插件 - 包含Java兼容性配置 -->
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <configuration>
        <!-- 强制使用Java 8环境配置 -->
        <skip>false</skip>
        <includes>
            <include>**/*</include>
        </includes>
        <!-- 排除不需要覆盖率统计的类 -->
        <excludes>
            <exclude>**/*Test*</exclude>
            <exclude>**/*Application*</exclude>
            <exclude>**/config/**</exclude>
        </excludes>
        <!-- 输出目录配置 -->
        <outputDirectory>${project.reporting.outputDirectory}/jacoco</outputDirectory>
        <!-- 数据文件配置 -->
        <dataFile>${project.build.directory}/jacoco.exec</dataFile>
        <!-- Java 8环境强制配置 -->
        <jvm>${java8.home}/bin/java</jvm>
        <!-- JVM参数配置，针对Java 8优化 -->
        <systemPropertyVariables>
            <java.home>${java8.home}</java.home>
            <java.security.policy>all.policy</java.security.policy>
        </systemPropertyVariables>
    </configuration>
    <executions>
        <!-- 准备JaCoCo代理 -->
        <execution>
            <id>prepare-agent</id>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
            <configuration>
                <!-- 配置代理参数，解决Java版本兼容性问题 -->
                <propertyName>surefireArgLine</propertyName>
                <destFile>${project.build.directory}/jacoco.exec</destFile>
                <!-- 添加JVM参数以解决JCE安全策略问题 -->
                <append>true</append>
            </configuration>
        </execution>
        <!-- 生成测试覆盖率报告 -->
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
            <configuration>
                <!-- 报告生成配置 -->
                <dataFile>${project.build.directory}/jacoco.exec</dataFile>
                <outputDirectory>${project.reporting.outputDirectory}/jacoco</outputDirectory>
                <!-- 跳过网络依赖，避免JCE安全策略问题 -->
                <skip>false</skip>
            </configuration>
        </execution>
        <!-- 聚合报告 -->
        <execution>
            <id>report-aggregate</id>
            <phase>verify</phase>
            <goals>
                <goal>report-aggregate</goal>
            </goals>
            <configuration>
                <!-- 聚合报告配置 -->
                <dataFileIncludes>
                    <dataFileInclude>**/jacoco.exec</dataFileInclude>
                </dataFileIncludes>
                <outputDirectory>${project.reporting.outputDirectory}/jacoco-aggregate</outputDirectory>
            </configuration>
        </execution>
    </executions>
</plugin>
