package com.sdesrd.filetransfer.client.config;

/**
 * 客户端配置构建器
 * 提供链式调用的配置方式，使配置更加友好和直观
 * 
 * <AUTHOR> SDK
 * @version 2.0.0
 */
public class ClientConfigBuilder {
    
    private final ClientConfig config;
    
    /**
     * 私有构造函数
     */
    private ClientConfigBuilder() {
        this.config = new ClientConfig();
    }
    
    /**
     * 创建配置构建器
     * 
     * @return 配置构建器实例
     */
    public static ClientConfigBuilder create() {
        return new ClientConfigBuilder();
    }
    
    /**
     * 设置服务器地址
     * 
     * @param serverAddr 服务器地址
     * @return 构建器实例
     */
    public ClientConfigBuilder serverAddr(String serverAddr) {
        config.getAuth().setServerAddr(serverAddr);
        return this;
    }
    
    /**
     * 设置服务器端口
     * 
     * @param serverPort 服务器端口
     * @return 构建器实例
     */
    public ClientConfigBuilder serverPort(int serverPort) {
        config.getAuth().setServerPort(serverPort);
        return this;
    }
    
    /**
     * 设置上下文路径
     * 
     * @param contextPath 上下文路径
     * @return 构建器实例
     */
    public ClientConfigBuilder contextPath(String contextPath) {
        config.getAuth().setContextPath(contextPath);
        return this;
    }
    
    /**
     * 启用HTTPS
     * 
     * @return 构建器实例
     */
    public ClientConfigBuilder useHttps() {
        config.getAuth().setUseHttps(true);
        return this;
    }
    
    /**
     * 设置用户认证信息
     * 
     * @param user 用户名
     * @param secretKey 密钥
     * @return 构建器实例
     */
    public ClientConfigBuilder auth(String user, String secretKey) {
        config.getAuth().setUser(user);
        config.getAuth().setSecretKey(secretKey);
        return this;
    }
    
    /**
     * 设置分片大小
     * 
     * @param chunkSize 分片大小（字节）
     * @return 构建器实例
     */
    public ClientConfigBuilder chunkSize(long chunkSize) {
        config.setChunkSize(chunkSize);
        return this;
    }
    
    /**
     * 设置最大并发传输数
     *
     * @param maxConcurrentTransfers 最大并发传输数
     * @return 构建器实例
     */
    public ClientConfigBuilder maxConcurrentTransfers(int maxConcurrentTransfers) {
        config.setMaxConcurrentTransfers(maxConcurrentTransfers);
        return this;
    }
    
    /**
     * 设置重试配置
     *
     * @param retryCount 重试次数
     * @param retryIntervalMs 重试间隔（毫秒）
     * @return 构建器实例
     */
    public ClientConfigBuilder retry(int retryCount, long retryIntervalMs) {
        config.setRetryCount(retryCount);
        config.setRetryIntervalMs(retryIntervalMs);
        return this;
    }
    
    /**
     * 构建配置对象
     * 
     * @return 配置对象
     * @throws IllegalStateException 如果配置不完整或无效
     */
    public ClientConfig build() {
        config.validateConfig();
        return config;
    }
    
    /**
     * 创建默认配置
     * 
     * @param user 用户名
     * @param secretKey 密钥
     * @return 默认配置
     */
    public static ClientConfig defaultConfig(String user, String secretKey) {
        return create()
                .auth(user, secretKey)
                .build();
    }
    
    /**
     * 创建本地开发配置
     *
     * @param user 用户名
     * @param secretKey 密钥
     * @return 本地开发配置
     */
    public static ClientConfig localConfig(String user, String secretKey) {
        return create()
                .serverAddr("localhost")
                .serverPort(49011)
                .auth(user, secretKey)
                .chunkSize(1024 * 1024) // 1MB
                .maxConcurrentTransfers(3)
                .retry(3, 1000)
                .build();
    }
    
    /**
     * 创建生产环境配置
     *
     * @param serverAddr 服务器地址
     * @param serverPort 服务器端口
     * @param user 用户名
     * @param secretKey 密钥
     * @return 生产环境配置
     */
    public static ClientConfig productionConfig(String serverAddr, int serverPort, String user, String secretKey) {
        return create()
                .serverAddr(serverAddr)
                .serverPort(serverPort)
                .useHttps()
                .auth(user, secretKey)
                .chunkSize(2 * 1024 * 1024) // 2MB
                .maxConcurrentTransfers(5)
                .retry(5, 2000)
                .build();
    }
}
