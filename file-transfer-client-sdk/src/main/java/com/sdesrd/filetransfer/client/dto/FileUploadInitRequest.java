package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * 文件上传初始化请求
 * 重构后的版本：移除文件名参数，仅保留文件后缀名
 */
@Data
public class FileUploadInitRequest {

    /**
     * 文件后缀名（不包含点号，如：jpg、pdf、txt等）
     * 可以为空字符串表示无后缀名文件
     */
    private String fileExtension;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件MD5值
     */
    private String fileMd5;

    /**
     * 分块大小（字节）
     */
    private Long chunkSize;
}