package com.sdesrd.filetransfer.client.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ClientAuthConfig 单元测试
 * 验证URL构建逻辑的正确性
 * 
 * <AUTHOR> SDK
 * @version 2.0.0
 */
@DisplayName("客户端认证配置测试")
class ClientAuthConfigTest {
    
    private ClientAuthConfig config;
    
    @BeforeEach
    void setUp() {
        config = new ClientAuthConfig();
        config.setUser("testUser");
        config.setSecretKey("testSecretKey");
    }
    
    @Test
    @DisplayName("默认配置应该生成正确的URL")
    void testDefaultConfiguration() {
        String expectedUrl = "http://localhost:49011";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("自定义服务器地址和端口")
    void testCustomServerAddressAndPort() {
        config.setServerAddr("example.com");
        config.setServerPort(9090);
        
        String expectedUrl = "http://example.com:9090";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("使用HTTPS协议")
    void testHttpsProtocol() {
        config.setUseHttps(true);
        
        String expectedUrl = "https://localhost:49011";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("设置上下文路径")
    void testContextPath() {
        config.setContextPath("api");
        
        String expectedUrl = "http://localhost:49011/api";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("上下文路径标准化 - 移除前后斜杠")
    void testContextPathNormalization() {
        // 测试前后都有斜杠的情况
        config.setContextPath("/api/v1/");
        assertEquals("http://localhost:49011/api/v1", config.getServerUrl());
        
        // 测试只有前斜杠的情况
        config.setContextPath("/api");
        assertEquals("http://localhost:49011/api", config.getServerUrl());
        
        // 测试只有后斜杠的情况
        config.setContextPath("api/");
        assertEquals("http://localhost:49011/api", config.getServerUrl());
        
        // 测试多个斜杠的情况
        config.setContextPath("///api/v1///");
        assertEquals("http://localhost:49011/api/v1", config.getServerUrl());
    }
    
    @Test
    @DisplayName("空的上下文路径")
    void testEmptyContextPath() {
        config.setContextPath("");
        assertEquals("http://localhost:49011", config.getServerUrl());
        
        config.setContextPath(null);
        assertEquals("http://localhost:49011", config.getServerUrl());
        
        config.setContextPath("   ");
        assertEquals("http://localhost:49011", config.getServerUrl());
    }
    
    @Test
    @DisplayName("复杂的URL构建场景")
    void testComplexUrlBuilding() {
        config.setServerAddr("api.example.com");
        config.setServerPort(443);
        config.setUseHttps(true);
        config.setContextPath("/file-transfer/v2/");
        
        String expectedUrl = "https://api.example.com:443/file-transfer/v2";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("验证配置 - 成功案例")
    void testValidConfiguration() {
        assertDoesNotThrow(() -> config.validate());
    }
    
    @Test
    @DisplayName("验证配置 - 服务器地址为空")
    void testValidationEmptyServerAddr() {
        config.setServerAddr("");
        assertThrows(IllegalStateException.class, () -> config.validate());
        
        config.setServerAddr(null);
        assertThrows(IllegalStateException.class, () -> config.validate());
        
        config.setServerAddr("   ");
        assertThrows(IllegalStateException.class, () -> config.validate());
    }
    
    @Test
    @DisplayName("验证配置 - 无效端口")
    void testValidationInvalidPort() {
        config.setServerPort(0);
        assertThrows(IllegalStateException.class, () -> config.validate());
        
        config.setServerPort(-1);
        assertThrows(IllegalStateException.class, () -> config.validate());
        
        config.setServerPort(65536);
        assertThrows(IllegalStateException.class, () -> config.validate());
    }
    
    @Test
    @DisplayName("验证配置 - 用户名为空")
    void testValidationEmptyUser() {
        config.setUser("");
        assertThrows(IllegalStateException.class, () -> config.validate());
        
        config.setUser(null);
        assertThrows(IllegalStateException.class, () -> config.validate());
        
        config.setUser("   ");
        assertThrows(IllegalStateException.class, () -> config.validate());
    }
    
    @Test
    @DisplayName("验证配置 - 密钥为空")
    void testValidationEmptySecretKey() {
        config.setSecretKey("");
        assertThrows(IllegalStateException.class, () -> config.validate());
        
        config.setSecretKey(null);
        assertThrows(IllegalStateException.class, () -> config.validate());
        
        config.setSecretKey("   ");
        assertThrows(IllegalStateException.class, () -> config.validate());
    }
    
    @Test
    @DisplayName("getServerUrl在无效配置时应该抛出异常")
    void testGetServerUrlWithInvalidConfig() {
        config.setServerAddr("");
        assertThrows(IllegalStateException.class, () -> config.getServerUrl());
        
        config.setServerAddr("localhost");
        config.setServerPort(-1);
        assertThrows(IllegalStateException.class, () -> config.getServerUrl());
    }
}
