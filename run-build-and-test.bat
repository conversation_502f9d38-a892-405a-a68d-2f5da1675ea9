@echo off
REM ================================================================================
REM 文件传输SDK构建和测试启动批处理文件
REM 功能：启动PowerShell脚本并处理执行策略
REM ================================================================================

echo.
echo ========================================================
echo     文件传输SDK统一构建和测试脚本 (Windows版本)
echo     启动器版本：2.0.0
echo ========================================================
echo.

REM 检查PowerShell是否可用
where powershell >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到PowerShell。请确保PowerShell已安装。
    pause
    exit /b 1
)

REM 检查PowerShell脚本是否存在
if not exist "build-and-test.ps1" (
    echo 错误：未找到PowerShell脚本文件 build-and-test.ps1
    pause
    exit /b 1
)

echo 正在启动PowerShell脚本...
echo.

REM 执行PowerShell脚本，绕过执行策略限制
powershell.exe -ExecutionPolicy Bypass -File "build-and-test.ps1" %*

REM 检查执行结果
if %errorlevel% equ 0 (
    echo.
    echo ========================================================
    echo 脚本执行成功完成
    echo ========================================================
) else (
    echo.
    echo ========================================================
    echo 脚本执行失败，退出码：%errorlevel%
    echo ========================================================
)

echo.
echo 按任意键退出...
pause >nul 