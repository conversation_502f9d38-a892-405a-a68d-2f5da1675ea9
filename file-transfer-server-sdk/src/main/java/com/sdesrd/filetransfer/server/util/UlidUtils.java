package com.sdesrd.filetransfer.server.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.regex.Pattern;

import com.github.f4b6a3.ulid.Ulid;
import com.github.f4b6a3.ulid.UlidCreator;

import lombok.extern.slf4j.Slf4j;

/**
 * ULID工具类
 * 提供ULID生成、验证和时间戳提取功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class UlidUtils {
    
    /**
     * ULID格式正则表达式
     * ULID格式：26个字符，使用Crockford's Base32编码
     */
    private static final Pattern ULID_PATTERN = Pattern.compile("^[0-9A-HJKMNP-TV-Z]{26}$");
    
    /**
     * 年月格式化器
     */
    private static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    
    /**
     * 时间戳提取的时区
     */
    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.systemDefault();
    
    /**
     * 生成新的ULID
     * 
     * @return ULID字符串
     */
    public static String generateUlid() {
        try {
            Ulid ulid = UlidCreator.getUlid();
            String ulidString = ulid.toString();
            log.debug("生成ULID: {}", ulidString);
            return ulidString;
        } catch (Exception e) {
            log.error("生成ULID失败", e);
            throw new RuntimeException("生成ULID失败", e);
        }
    }
    
    /**
     * 生成指定时间戳的ULID
     * 
     * @param timestamp 时间戳（毫秒）
     * @return ULID字符串
     */
    public static String generateUlid(long timestamp) {
        try {
            Ulid ulid = UlidCreator.getUlid(timestamp);
            String ulidString = ulid.toString();
            log.debug("生成指定时间戳的ULID: {} (timestamp: {})", ulidString, timestamp);
            return ulidString;
        } catch (Exception e) {
            log.error("生成指定时间戳的ULID失败 - timestamp: {}", timestamp, e);
            throw new RuntimeException("生成指定时间戳的ULID失败", e);
        }
    }
    
    /**
     * 验证ULID格式是否有效
     * 
     * @param ulid ULID字符串
     * @return 是否有效
     */
    public static boolean isValidUlid(String ulid) {
        if (ulid == null || ulid.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 检查格式
            if (!ULID_PATTERN.matcher(ulid.trim().toUpperCase()).matches()) {
                log.debug("ULID格式不匹配: {}", ulid);
                return false;
            }
            
            // 尝试解析ULID
            Ulid.from(ulid);
            return true;
            
        } catch (Exception e) {
            log.debug("ULID验证失败: {} - {}", ulid, e.getMessage());
            return false;
        }
    }
    
    /**
     * 从ULID提取时间戳
     * 
     * @param ulid ULID字符串
     * @return 时间戳（毫秒），如果ULID无效则返回null
     */
    public static Long extractTimestamp(String ulid) {
        if (!isValidUlid(ulid)) {
            log.warn("无效的ULID，无法提取时间戳: {}", ulid);
            return null;
        }
        
        try {
            Ulid ulidObj = Ulid.from(ulid);
            long timestamp = ulidObj.getTime();
            log.debug("从ULID提取时间戳: {} -> {}", ulid, timestamp);
            return timestamp;
        } catch (Exception e) {
            log.error("从ULID提取时间戳失败: {}", ulid, e);
            return null;
        }
    }
    
    /**
     * 从ULID提取年月信息（YYYYMM格式）
     * 
     * @param ulid ULID字符串
     * @return 年月字符串（如：202406），如果ULID无效则返回null
     */
    public static String extractYearMonth(String ulid) {
        Long timestamp = extractTimestamp(ulid);
        if (timestamp == null) {
            return null;
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp), 
                DEFAULT_ZONE_ID
            );
            String yearMonth = dateTime.format(YEAR_MONTH_FORMATTER);
            log.debug("从ULID提取年月: {} -> {}", ulid, yearMonth);
            return yearMonth;
        } catch (Exception e) {
            log.error("从ULID提取年月失败: {} (timestamp: {})", ulid, timestamp, e);
            return null;
        }
    }
    
    /**
     * 从ULID提取Instant对象
     * 
     * @param ulid ULID字符串
     * @return Instant对象，如果ULID无效则返回null
     */
    public static Instant extractInstant(String ulid) {
        Long timestamp = extractTimestamp(ulid);
        if (timestamp == null) {
            return null;
        }
        
        try {
            return Instant.ofEpochMilli(timestamp);
        } catch (Exception e) {
            log.error("从ULID提取Instant失败: {} (timestamp: {})", ulid, timestamp, e);
            return null;
        }
    }
    
    /**
     * 从ULID提取LocalDateTime对象
     * 
     * @param ulid ULID字符串
     * @return LocalDateTime对象，如果ULID无效则返回null
     */
    public static LocalDateTime extractLocalDateTime(String ulid) {
        Instant instant = extractInstant(ulid);
        if (instant == null) {
            return null;
        }
        
        try {
            return LocalDateTime.ofInstant(instant, DEFAULT_ZONE_ID);
        } catch (Exception e) {
            log.error("从ULID提取LocalDateTime失败: {}", ulid, e);
            return null;
        }
    }
    
    /**
     * 检查ULID是否在指定时间范围内
     * 
     * @param ulid ULID字符串
     * @param startTime 开始时间（毫秒时间戳）
     * @param endTime 结束时间（毫秒时间戳）
     * @return 是否在时间范围内
     */
    public static boolean isInTimeRange(String ulid, long startTime, long endTime) {
        Long timestamp = extractTimestamp(ulid);
        if (timestamp == null) {
            return false;
        }
        
        return timestamp >= startTime && timestamp <= endTime;
    }
    
    /**
     * 比较两个ULID的时间戳
     * 
     * @param ulid1 第一个ULID
     * @param ulid2 第二个ULID
     * @return 比较结果：负数表示ulid1早于ulid2，0表示相等，正数表示ulid1晚于ulid2
     *         如果任一ULID无效，则返回null
     */
    public static Integer compareTimestamp(String ulid1, String ulid2) {
        Long timestamp1 = extractTimestamp(ulid1);
        Long timestamp2 = extractTimestamp(ulid2);
        
        if (timestamp1 == null || timestamp2 == null) {
            log.warn("无法比较ULID时间戳，存在无效ULID: {} vs {}", ulid1, ulid2);
            return null;
        }
        
        return Long.compare(timestamp1, timestamp2);
    }
    
    /**
     * 格式化ULID信息为可读字符串
     * 
     * @param ulid ULID字符串
     * @return 格式化的信息字符串
     */
    public static String formatUlidInfo(String ulid) {
        if (!isValidUlid(ulid)) {
            return "无效的ULID: " + ulid;
        }
        
        Long timestamp = extractTimestamp(ulid);
        String yearMonth = extractYearMonth(ulid);
        LocalDateTime dateTime = extractLocalDateTime(ulid);
        
        return String.format("ULID: %s, 时间戳: %d, 年月: %s, 日期时间: %s", 
            ulid, timestamp, yearMonth, 
            dateTime != null ? dateTime.toString() : "N/A");
    }
}
