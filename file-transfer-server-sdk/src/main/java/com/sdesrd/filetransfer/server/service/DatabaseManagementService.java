package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileTransferException;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.util.UlidUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库管理服务
 * 提供数据库备份、恢复、重建和健康检查功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class DatabaseManagementService {
    
    @Autowired
    private FileTransferProperties properties;
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private FileTransferRecordMapper transferRecordMapper;
    
    /**
     * 定时任务执行器
     */
    private ScheduledExecutorService scheduledExecutor;
    
    /**
     * 备份文件名格式化器
     */
    private static final DateTimeFormatter BACKUP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    /**
     * 备份文件扩展名
     */
    private static final String BACKUP_EXTENSION = ".backup.db";
    
    /**
     * 自动备份间隔（小时）
     */
    private static final int AUTO_BACKUP_INTERVAL_HOURS = 24;
    
    /**
     * 备份文件保留天数
     */
    private static final int BACKUP_RETENTION_DAYS = 7;
    
    @PostConstruct
    public void init() {
        // 启动定时备份任务
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "database-backup-scheduler");
            thread.setDaemon(true);
            return thread;
        });
        
        // 每24小时执行一次自动备份
        scheduledExecutor.scheduleAtFixedRate(
            this::performAutoBackup, 
            AUTO_BACKUP_INTERVAL_HOURS, 
            AUTO_BACKUP_INTERVAL_HOURS, 
            TimeUnit.HOURS
        );
        
        log.info("数据库管理服务初始化完成，自动备份间隔: {}小时", AUTO_BACKUP_INTERVAL_HOURS);
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("数据库管理服务已关闭");
    }
    
    /**
     * 检查数据库健康状态
     * 
     * @return 健康状态信息
     */
    public DatabaseHealthInfo checkDatabaseHealth() {
        DatabaseHealthInfo healthInfo = new DatabaseHealthInfo();
        
        try {
            // 检查数据库连接
            try (Connection connection = dataSource.getConnection()) {
                healthInfo.setConnectionAvailable(true);
                healthInfo.setDatabasePath(properties.getDatabasePath());

                // 检查数据库文件
                File dbFile = new File(properties.getDatabasePath());
                healthInfo.setDatabaseFileExists(dbFile.exists());
                healthInfo.setDatabaseFileSize(dbFile.exists() ? dbFile.length() : 0);
                
                // 检查表是否可访问
                try {
                    QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
                    query.last("LIMIT 1");
                    transferRecordMapper.selectList(query);
                    healthInfo.setTablesAccessible(true);
                } catch (Exception e) {
                    log.warn("数据库表访问检查失败", e);
                    healthInfo.setTablesAccessible(false);
                    healthInfo.setErrorMessage("数据库表不可访问: " + e.getMessage());
                }
                
                // 统计记录数量
                try {
                    long totalRecords = transferRecordMapper.selectCount(null);
                    healthInfo.setTotalRecords(totalRecords);
                } catch (Exception e) {
                    log.warn("统计记录数量失败", e);
                    healthInfo.setTotalRecords(-1L);
                }
                
            }
        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            healthInfo.setConnectionAvailable(false);
            healthInfo.setErrorMessage("数据库连接失败: " + e.getMessage());
        }
        
        healthInfo.setCheckTime(LocalDateTime.now().toString());
        return healthInfo;
    }
    
    /**
     * 创建数据库备份
     * 
     * @return 备份文件路径
     */
    public String createBackup() {
        try {
            String dbPath = properties.getDatabasePath();
            File dbFile = new File(dbPath);
            
            if (!dbFile.exists()) {
                throw new FileTransferException("数据库文件不存在: " + dbPath);
            }
            
            // 生成备份文件名
            String timestamp = LocalDateTime.now().format(BACKUP_FORMATTER);
            String backupFileName = "database_" + timestamp + BACKUP_EXTENSION;
            
            // 确保备份目录存在
            File dbDir = dbFile.getParentFile();
            File backupDir = new File(dbDir, "backups");
            if (!backupDir.exists()) {
                backupDir.mkdirs();
            }
            
            File backupFile = new File(backupDir, backupFileName);
            
            // 复制数据库文件
            Files.copy(dbFile.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            
            log.info("数据库备份创建成功: {}", backupFile.getAbsolutePath());
            return backupFile.getAbsolutePath();
            
        } catch (Exception e) {
            log.error("创建数据库备份失败", e);
            throw new FileTransferException("创建数据库备份失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有备份文件列表
     * 
     * @return 备份文件信息列表
     */
    public List<BackupFileInfo> listBackups() {
        List<BackupFileInfo> backups = new ArrayList<>();
        
        try {
            String dbPath = properties.getDatabasePath();
            File dbFile = new File(dbPath);
            File backupDir = new File(dbFile.getParentFile(), "backups");
            
            if (!backupDir.exists() || !backupDir.isDirectory()) {
                return backups;
            }
            
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(
                    backupDir.toPath(), "*" + BACKUP_EXTENSION)) {
                
                for (Path backupPath : stream) {
                    File backupFile = backupPath.toFile();
                    BackupFileInfo info = new BackupFileInfo();
                    info.setFileName(backupFile.getName());
                    info.setFilePath(backupFile.getAbsolutePath());
                    info.setFileSize(backupFile.length());
                    info.setCreateTime(backupFile.lastModified());
                    info.setFormattedSize(formatFileSize(backupFile.length()));
                    info.setFormattedTime(formatTime(backupFile.lastModified()));
                    
                    backups.add(info);
                }
            }
            
            // 按创建时间倒序排列
            backups.sort((a, b) -> Long.compare(b.getCreateTime(), a.getCreateTime()));
            
        } catch (Exception e) {
            log.error("获取备份文件列表失败", e);
        }
        
        return backups;
    }
    
    /**
     * 下载备份文件
     * 
     * @param fileName 备份文件名
     * @return 备份文件对象
     */
    public File getBackupFile(String fileName) {
        try {
            String dbPath = properties.getDatabasePath();
            File dbFile = new File(dbPath);
            File backupDir = new File(dbFile.getParentFile(), "backups");
            File backupFile = new File(backupDir, fileName);
            
            // 安全检查：确保文件在备份目录内
            if (!backupFile.getCanonicalPath().startsWith(backupDir.getCanonicalPath())) {
                throw new FileTransferException("非法的备份文件路径: " + fileName);
            }
            
            if (!backupFile.exists() || !backupFile.isFile()) {
                throw new FileTransferException("备份文件不存在: " + fileName);
            }
            
            if (!fileName.endsWith(BACKUP_EXTENSION)) {
                throw new FileTransferException("非法的备份文件格式: " + fileName);
            }
            
            return backupFile;
            
        } catch (IOException e) {
            log.error("获取备份文件失败: {}", fileName, e);
            throw new FileTransferException("获取备份文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 通过磁盘扫描重建数据库
     * 
     * @return 重建结果信息
     */
    @Transactional
    public DatabaseRebuildResult rebuildDatabaseFromDisk() {
        DatabaseRebuildResult result = new DatabaseRebuildResult();
        result.setStartTime(LocalDateTime.now().toString());
        
        try {
            log.info("开始通过磁盘扫描重建数据库");
            
            // 清空现有记录
            transferRecordMapper.delete(null);
            log.info("已清空现有数据库记录");
            
            // 扫描存储目录
            String storagePath = properties.getDefaultConfig().getStoragePath();
            File storageDir = new File(storagePath);
            
            if (!storageDir.exists() || !storageDir.isDirectory()) {
                throw new FileTransferException("存储目录不存在: " + storagePath);
            }
            
            result = scanAndRebuildRecords(storageDir, result);
            
            result.setEndTime(LocalDateTime.now().toString());
            result.setSuccess(true);
            
            log.info("数据库重建完成 - 扫描文件: {}, 成功重建: {}, 失败: {}", 
                result.getScannedFiles(), result.getRebuiltRecords(), result.getFailedFiles());
            
        } catch (Exception e) {
            log.error("数据库重建失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now().toString());
        }
        
        return result;
    }
    
    /**
     * 执行自动备份
     */
    private void performAutoBackup() {
        try {
            log.info("开始执行自动数据库备份");
            String backupPath = createBackup();
            
            // 清理过期备份
            cleanupOldBackups();
            
            log.info("自动数据库备份完成: {}", backupPath);
            
        } catch (Exception e) {
            log.error("自动数据库备份失败", e);
        }
    }
    
    /**
     * 清理过期备份文件
     */
    private void cleanupOldBackups() {
        try {
            List<BackupFileInfo> backups = listBackups();
            long cutoffTime = System.currentTimeMillis() - (BACKUP_RETENTION_DAYS * 24 * 60 * 60 * 1000L);
            
            int deletedCount = 0;
            for (BackupFileInfo backup : backups) {
                if (backup.getCreateTime() < cutoffTime) {
                    File backupFile = new File(backup.getFilePath());
                    if (backupFile.delete()) {
                        deletedCount++;
                        log.debug("删除过期备份文件: {}", backup.getFileName());
                    }
                }
            }
            
            if (deletedCount > 0) {
                log.info("清理过期备份文件完成，删除数量: {}", deletedCount);
            }
            
        } catch (Exception e) {
            log.error("清理过期备份文件失败", e);
        }
    }
    
    /**
     * 扫描目录并重建记录
     */
    private DatabaseRebuildResult scanAndRebuildRecords(File dir, DatabaseRebuildResult result) {
        File[] files = dir.listFiles();
        if (files == null) {
            return result;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归扫描子目录
                scanAndRebuildRecords(file, result);
            } else if (file.isFile()) {
                result.setScannedFiles(result.getScannedFiles() + 1);
                
                try {
                    // 尝试重建文件记录
                    if (rebuildFileRecord(file)) {
                        result.setRebuiltRecords(result.getRebuiltRecords() + 1);
                    }
                } catch (Exception e) {
                    log.warn("重建文件记录失败: {} - {}", file.getAbsolutePath(), e.getMessage());
                    result.setFailedFiles(result.getFailedFiles() + 1);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 重建单个文件记录
     */
    private boolean rebuildFileRecord(File file) throws IOException {
        // 计算文件MD5
        String md5 = FileUtils.calculateFileMD5(file);
        
        // 生成ULID作为记录ID
        String recordId = UlidUtils.generateUlid();
        
        // 创建文件传输记录
        FileTransferRecord record = new FileTransferRecord();
        record.setId(recordId);
        record.setFileId(md5);
        record.setFileName(file.getName());
        record.setFileSize(file.length());
        record.setFilePath(file.getAbsolutePath());
        record.setTransferredSize(file.length());
        record.setTotalChunks(1);
        record.setCompletedChunks(1);
        record.setStatus(2); // 传输完成
        record.setCreateTime(LocalDateTime.now().toString());
        record.setUpdateTime(LocalDateTime.now().toString());
        record.setCompleteTime(LocalDateTime.now().toString());
        
        // 插入数据库
        transferRecordMapper.insert(record);
        
        log.debug("重建文件记录: {} -> {}", file.getAbsolutePath(), md5);
        return true;
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 格式化时间
     */
    private String formatTime(long timestamp) {
        return LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(timestamp),
            java.time.ZoneId.systemDefault()
        ).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 数据库健康信息
     */
    public static class DatabaseHealthInfo {
        private boolean connectionAvailable;
        private boolean databaseFileExists;
        private boolean tablesAccessible;
        private String databasePath;
        private long databaseFileSize;
        private long totalRecords;
        private String checkTime;
        private String errorMessage;
        
        // Getters and Setters
        public boolean isConnectionAvailable() { return connectionAvailable; }
        public void setConnectionAvailable(boolean connectionAvailable) { this.connectionAvailable = connectionAvailable; }
        
        public boolean isDatabaseFileExists() { return databaseFileExists; }
        public void setDatabaseFileExists(boolean databaseFileExists) { this.databaseFileExists = databaseFileExists; }
        
        public boolean isTablesAccessible() { return tablesAccessible; }
        public void setTablesAccessible(boolean tablesAccessible) { this.tablesAccessible = tablesAccessible; }
        
        public String getDatabasePath() { return databasePath; }
        public void setDatabasePath(String databasePath) { this.databasePath = databasePath; }
        
        public long getDatabaseFileSize() { return databaseFileSize; }
        public void setDatabaseFileSize(long databaseFileSize) { this.databaseFileSize = databaseFileSize; }
        
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }
        
        public String getCheckTime() { return checkTime; }
        public void setCheckTime(String checkTime) { this.checkTime = checkTime; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    /**
     * 备份文件信息
     */
    public static class BackupFileInfo {
        private String fileName;
        private String filePath;
        private long fileSize;
        private long createTime;
        private String formattedSize;
        private String formattedTime;
        
        // Getters and Setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }
        
        public String getFormattedSize() { return formattedSize; }
        public void setFormattedSize(String formattedSize) { this.formattedSize = formattedSize; }
        
        public String getFormattedTime() { return formattedTime; }
        public void setFormattedTime(String formattedTime) { this.formattedTime = formattedTime; }
    }
    
    /**
     * 数据库重建结果
     */
    public static class DatabaseRebuildResult {
        private boolean success;
        private String startTime;
        private String endTime;
        private int scannedFiles;
        private int rebuiltRecords;
        private int failedFiles;
        private String errorMessage;
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getStartTime() { return startTime; }
        public void setStartTime(String startTime) { this.startTime = startTime; }
        
        public String getEndTime() { return endTime; }
        public void setEndTime(String endTime) { this.endTime = endTime; }
        
        public int getScannedFiles() { return scannedFiles; }
        public void setScannedFiles(int scannedFiles) { this.scannedFiles = scannedFiles; }
        
        public int getRebuiltRecords() { return rebuiltRecords; }
        public void setRebuiltRecords(int rebuiltRecords) { this.rebuiltRecords = rebuiltRecords; }
        
        public int getFailedFiles() { return failedFiles; }
        public void setFailedFiles(int failedFiles) { this.failedFiles = failedFiles; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
