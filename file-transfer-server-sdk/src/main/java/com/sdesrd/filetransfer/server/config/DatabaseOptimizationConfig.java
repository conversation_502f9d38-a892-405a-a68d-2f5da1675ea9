package com.sdesrd.filetransfer.server.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库优化配置类
 * 
 * 专门用于优化SQLite数据库的并发性能和事务处理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-19
 */
@Slf4j
@Configuration
@EnableTransactionManagement
@ConditionalOnProperty(prefix = "file.transfer.server", name = "enabled", havingValue = "true", matchIfMissing = true)
public class DatabaseOptimizationConfig {

    @Autowired
    private DataSource dataSource;

    /**
     * 配置事务管理器
     * 
     * @return 事务管理器
     */
    @Bean
    public PlatformTransactionManager transactionManager() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager(dataSource);
        
        // 设置事务超时时间（秒）
        transactionManager.setDefaultTimeout(30);
        
        // 设置事务隔离级别为读已提交（SQLite支持的最高级别）
        transactionManager.setGlobalRollbackOnParticipationFailure(false);
        
        log.info("数据库事务管理器配置完成 - 默认超时: 30秒");
        
        return transactionManager;
    }

    /**
     * 配置JdbcTemplate
     * 
     * @return JdbcTemplate实例
     */
    @Bean
    public JdbcTemplate jdbcTemplate() {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        
        // 设置查询超时时间（秒）
        jdbcTemplate.setQueryTimeout(30);
        
        // 设置最大行数限制
        jdbcTemplate.setMaxRows(10000);
        
        // 设置获取大小
        jdbcTemplate.setFetchSize(100);
        
        log.info("JdbcTemplate配置完成 - 查询超时: 30秒, 最大行数: 10000");
        
        return jdbcTemplate;
    }

    /**
     * 数据库优化初始化
     * 
     * 在应用启动时执行SQLite优化设置
     */
    @Bean
    public DatabaseOptimizer databaseOptimizer() {
        return new DatabaseOptimizer(dataSource);
    }

    /**
     * 数据库优化器内部类
     */
    public static class DatabaseOptimizer {
        
        private final DataSource dataSource;
        
        public DatabaseOptimizer(DataSource dataSource) {
            this.dataSource = dataSource;
            optimizeDatabase();
        }
        
        /**
         * 执行数据库优化设置
         */
        private void optimizeDatabase() {
            try {
                JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
                
                log.info("开始执行SQLite数据库优化设置...");
                
                // 设置WAL模式（Write-Ahead Logging）以提高并发性能
                try {
                    jdbcTemplate.execute("PRAGMA journal_mode=WAL");
                    log.info("✅ 启用WAL模式成功");
                } catch (Exception e) {
                    log.warn("启用WAL模式失败: {}", e.getMessage());
                }
                
                // 设置同步模式为NORMAL以平衡性能和安全性
                try {
                    jdbcTemplate.execute("PRAGMA synchronous=NORMAL");
                    log.info("✅ 设置同步模式为NORMAL成功");
                } catch (Exception e) {
                    log.warn("设置同步模式失败: {}", e.getMessage());
                }
                
                // 设置缓存大小（页数）
                try {
                    jdbcTemplate.execute("PRAGMA cache_size=10000");
                    log.info("✅ 设置缓存大小为10000页成功");
                } catch (Exception e) {
                    log.warn("设置缓存大小失败: {}", e.getMessage());
                }
                
                // 设置临时存储为内存
                try {
                    jdbcTemplate.execute("PRAGMA temp_store=memory");
                    log.info("✅ 设置临时存储为内存成功");
                } catch (Exception e) {
                    log.warn("设置临时存储失败: {}", e.getMessage());
                }
                
                // 设置忙等待超时时间（毫秒）
                try {
                    jdbcTemplate.execute("PRAGMA busy_timeout=30000");
                    log.info("✅ 设置忙等待超时为30秒成功");
                } catch (Exception e) {
                    log.warn("设置忙等待超时失败: {}", e.getMessage());
                }
                
                // 启用外键约束
                try {
                    jdbcTemplate.execute("PRAGMA foreign_keys=ON");
                    log.info("✅ 启用外键约束成功");
                } catch (Exception e) {
                    log.warn("启用外键约束失败: {}", e.getMessage());
                }
                
                // 设置页面大小（必须在创建数据库时设置，这里只是尝试）
                try {
                    jdbcTemplate.execute("PRAGMA page_size=4096");
                    log.info("✅ 设置页面大小为4096字节成功");
                } catch (Exception e) {
                    log.warn("设置页面大小失败: {}", e.getMessage());
                }
                
                // 启用查询优化器
                try {
                    jdbcTemplate.execute("PRAGMA optimize");
                    log.info("✅ 启用查询优化器成功");
                } catch (Exception e) {
                    log.warn("启用查询优化器失败: {}", e.getMessage());
                }
                
                // 检查当前设置
                logCurrentSettings(jdbcTemplate);
                
                log.info("SQLite数据库优化设置完成");
                
            } catch (Exception e) {
                log.error("数据库优化设置失败", e);
                // 不抛出异常，让应用继续启动
            }
        }
        
        /**
         * 记录当前数据库设置
         * 
         * @param jdbcTemplate JDBC模板
         */
        private void logCurrentSettings(JdbcTemplate jdbcTemplate) {
            try {
                log.info("=== 当前SQLite数据库设置 ===");
                
                // 查询并记录各种设置
                String[] pragmas = {
                    "journal_mode", "synchronous", "cache_size", 
                    "temp_store", "busy_timeout", "foreign_keys",
                    "page_size", "page_count"
                };
                
                for (String pragma : pragmas) {
                    try {
                        String value = jdbcTemplate.queryForObject(
                            "PRAGMA " + pragma, String.class);
                        log.info("{}: {}", pragma, value);
                    } catch (Exception e) {
                        log.warn("查询{}失败: {}", pragma, e.getMessage());
                    }
                }
                
                log.info("=== 数据库设置记录完成 ===");
                
            } catch (Exception e) {
                log.warn("记录数据库设置失败: {}", e.getMessage());
            }
        }
    }
}
