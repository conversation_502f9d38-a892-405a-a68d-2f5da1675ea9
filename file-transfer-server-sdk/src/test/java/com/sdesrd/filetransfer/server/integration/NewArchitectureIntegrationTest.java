package com.sdesrd.filetransfer.server.integration;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.io.TempDir;


import com.sdesrd.filetransfer.server.dto.FileUploadInitRequest;
import com.sdesrd.filetransfer.server.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.server.dto.FileUploadCompleteResponse;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.util.UlidUtils;
// 移除文件权限保护机制 - 删除FilePermissionUtils导入

import static org.junit.jupiter.api.Assertions.*;

/**
 * 新架构集成测试
 * 验证ULID存储结构、增强秒传、文件权限管理等新功能的完整工作流程
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("新架构集成测试")
class NewArchitectureIntegrationTest {
    
    @TempDir
    Path tempDir;
    
    private String testFileContent;
    private String testFileMd5;
    private String testFileExtension;
    
    @BeforeEach
    void setUp() throws IOException {
        // 准备测试数据
        testFileContent = "这是一个测试文件的内容，用于验证新的文件传输架构功能。";
        testFileMd5 = calculateMd5(testFileContent);
        testFileExtension = "txt";
    }
    
    @Test
    @DisplayName("ULID生成和验证完整流程测试")
    void testUlidWorkflow() {
        // 1. 生成ULID
        String ulid = UlidUtils.generateUlid();
        assertNotNull(ulid, "ULID不应为null");
        assertTrue(UlidUtils.isValidUlid(ulid), "生成的ULID应有效");
        
        // 2. 从ULID提取时间信息
        String yearMonth = UlidUtils.extractYearMonth(ulid);
        assertNotNull(yearMonth, "应能从ULID提取年月信息");
        assertEquals(6, yearMonth.length(), "年月信息应为6位");
        
        // 3. 验证ULID时间戳
        Long timestamp = UlidUtils.extractTimestamp(ulid);
        assertNotNull(timestamp, "应能从ULID提取时间戳");
        assertTrue(timestamp > 0, "时间戳应大于0");
        
        // 4. 验证ULID格式化信息
        String info = UlidUtils.formatUlidInfo(ulid);
        assertTrue(info.contains(ulid), "格式化信息应包含ULID");
        assertTrue(info.contains(yearMonth), "格式化信息应包含年月");
    }
    
    @Test
    @DisplayName("新存储路径结构测试")
    void testNewStoragePathStructure() throws IOException {
        // 1. 生成ULID作为fileId
        String fileId = UlidUtils.generateUlid();
        String yearMonth = UlidUtils.extractYearMonth(fileId);
        
        // 2. 构建新的存储路径结构
        String basePath = tempDir.toString();
        String fileName = testFileMd5 + "." + testFileExtension;
        Path expectedPath = tempDir.resolve(yearMonth).resolve(fileId).resolve(fileName);
        
        // 3. 创建目录结构
        Files.createDirectories(expectedPath.getParent());
        
        // 4. 创建测试文件
        Files.write(expectedPath, testFileContent.getBytes());
        
        // 5. 验证文件存在且路径正确
        assertTrue(Files.exists(expectedPath), "文件应存在于新的路径结构中");
        assertTrue(expectedPath.toString().contains(yearMonth), "路径应包含年月信息");
        assertTrue(expectedPath.toString().contains(fileId), "路径应包含fileId");
        assertTrue(expectedPath.toString().endsWith(fileName), "路径应以正确的文件名结尾");
        
        // 6. 验证文件内容
        String actualContent = new String(Files.readAllBytes(expectedPath));
        assertEquals(testFileContent, actualContent, "文件内容应正确");
    }
    
    @Test
    @DisplayName("文件基本操作测试")
    void testBasicFileOperations() throws IOException {
        // 移除文件权限保护机制 - 改为测试基本文件操作
        // 1. 创建测试文件
        Path testFile = tempDir.resolve("basic-operation-test.txt");
        Files.write(testFile, testFileContent.getBytes());
        File file = testFile.toFile();

        // 2. 验证文件基本属性
        assertTrue(file.exists(), "文件应存在");
        assertTrue(file.canRead(), "文件应可读");
        assertTrue(file.canWrite(), "文件应可写");
        assertTrue(file.isFile(), "应为普通文件");

        // 3. 验证文件内容
        String content = new String(Files.readAllBytes(testFile));
        assertEquals(testFileContent, content, "文件内容应正确");

        // 4. 测试文件修改
        String appendedContent = testFileContent + "\n追加内容";
        Files.write(testFile, appendedContent.getBytes());

        // 5. 验证修改后的内容
        String modifiedContent = new String(Files.readAllBytes(testFile));
        assertEquals(appendedContent, modifiedContent, "修改后的文件内容应正确");
        assertTrue(modifiedContent.contains("追加内容"), "文件内容应包含追加的内容");

        // 6. 验证文件大小变化
        assertTrue(file.length() > testFileContent.length(), "文件大小应增加");

        // 7. 测试文件删除
        assertTrue(file.delete(), "文件应能被删除");
        assertFalse(file.exists(), "删除后文件应不存在");
    }
    
    @Test
    @DisplayName("文件上传初始化请求结构测试")
    void testFileUploadInitRequestStructure() {
        // 1. 创建上传初始化请求
        FileUploadInitRequest request = new FileUploadInitRequest();
        request.setFileExtension(testFileExtension);
        request.setFileSize(1024L);
        request.setFileMd5(testFileMd5);
        request.setChunkSize(512L);
        
        // 2. 验证请求结构
        assertEquals(testFileExtension, request.getFileExtension(), "文件后缀应正确");
        assertEquals(1024L, request.getFileSize().longValue(), "文件大小应正确");
        assertEquals(testFileMd5, request.getFileMd5(), "文件MD5应正确");
        assertEquals(512L, request.getChunkSize().longValue(), "分块大小应正确");
        
        // 3. 测试空后缀名情况
        request.setFileExtension("");
        assertEquals("", request.getFileExtension(), "应支持空后缀名");
        
        request.setFileExtension(null);
        assertNull(request.getFileExtension(), "应支持null后缀名");
    }
    
    @Test
    @DisplayName("文件上传完成响应结构测试")
    void testFileUploadCompleteResponseStructure() {
        // 1. 创建上传完成响应
        FileUploadCompleteResponse response = new FileUploadCompleteResponse();
        String transferId = "test-transfer-id";
        String fileId = UlidUtils.generateUlid();
        String fileName = testFileMd5 + "." + testFileExtension;
        String relativePath = UlidUtils.extractYearMonth(fileId) + "/" + fileId + "/" + fileName;
        
        // 2. 设置响应数据
        response.setTransferId(transferId);
        response.setFileId(fileId);
        response.setFileName(fileName);
        response.setRelativePath(relativePath);
        response.setFileSize(1024L);
        response.setFileMd5(testFileMd5);
        response.setCompleteTime("2023-12-31T00:00:00Z");
        response.setFastUpload(false);
        response.setTransferDuration(5000L);
        
        // 3. 验证响应结构
        assertEquals(transferId, response.getTransferId(), "传输ID应正确");
        assertEquals(fileId, response.getFileId(), "文件ID应正确");
        assertEquals(fileName, response.getFileName(), "文件名应正确");
        assertEquals(relativePath, response.getRelativePath(), "相对路径应正确");
        assertEquals(1024L, response.getFileSize().longValue(), "文件大小应正确");
        assertEquals(testFileMd5, response.getFileMd5(), "文件MD5应正确");
        assertEquals("2023-12-31T00:00:00Z", response.getCompleteTime(), "完成时间应正确");
        assertFalse(response.getFastUpload(), "秒传标志应正确");
        assertEquals(5000L, response.getTransferDuration().longValue(), "传输耗时应正确");
        
        // 4. 验证相对路径格式
        assertTrue(relativePath.contains(UlidUtils.extractYearMonth(fileId)), "相对路径应包含年月");
        assertTrue(relativePath.contains(fileId), "相对路径应包含fileId");
        assertTrue(relativePath.endsWith(fileName), "相对路径应以文件名结尾");
    }
    
    @Test
    @DisplayName("MD5计算和文件名生成测试")
    void testMd5CalculationAndFileNaming() {
        // 1. 测试不同后缀名的文件名生成
        String[] extensions = {"txt", "pdf", "jpg", "doc", ""};
        
        for (String ext : extensions) {
            String expectedFileName = ext.isEmpty() ? testFileMd5 : testFileMd5 + "." + ext;
            
            // 验证文件名格式
            if (ext.isEmpty()) {
                assertEquals(testFileMd5, expectedFileName, "无后缀文件名应只包含MD5");
            } else {
                assertTrue(expectedFileName.startsWith(testFileMd5), "文件名应以MD5开头");
                assertTrue(expectedFileName.endsWith("." + ext), "文件名应以正确后缀结尾");
            }
        }
        
        // 2. 验证MD5计算的一致性
        String md5_1 = calculateMd5(testFileContent);
        String md5_2 = calculateMd5(testFileContent);
        assertEquals(md5_1, md5_2, "相同内容的MD5应一致");
        
        // 3. 验证不同内容的MD5不同
        String differentContent = testFileContent + "不同";
        String differentMd5 = calculateMd5(differentContent);
        assertNotEquals(testFileMd5, differentMd5, "不同内容的MD5应不同");
    }
    
    @Test
    @DisplayName("错误处理和边界情况测试")
    void testErrorHandlingAndEdgeCases() {
        // 1. 测试无效ULID处理
        assertFalse(UlidUtils.isValidUlid("invalid-ulid"), "无效ULID应被识别");
        assertNull(UlidUtils.extractYearMonth("invalid-ulid"), "无效ULID提取年月应返回null");
        
        // 2. 测试空文件处理
        String emptyContent = "";
        String emptyMd5 = calculateMd5(emptyContent);
        assertNotNull(emptyMd5, "空文件MD5不应为null");
        assertTrue(emptyMd5.length() == 32, "空文件MD5长度应为32");
        
        // 3. 测试特殊字符文件名
        String specialContent = "特殊字符测试：!@#$%^&*()_+-=[]{}|;':\",./<>?";
        String specialMd5 = calculateMd5(specialContent);
        assertNotNull(specialMd5, "特殊字符内容MD5不应为null");
        assertTrue(specialMd5.matches("[0-9a-f]{32}"), "MD5应为32位十六进制字符");
    }
    
    /**
     * 计算字符串的MD5值
     */
    private String calculateMd5(String content) {
        try {
            return FileUtils.calculateMD5(content.getBytes());
        } catch (Exception e) {
            throw new RuntimeException("计算MD5失败", e);
        }
    }
}
