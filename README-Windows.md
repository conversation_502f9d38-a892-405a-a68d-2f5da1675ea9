# 文件传输SDK统一构建和测试脚本 - Windows版本

## 📋 概述

这是原始bash脚本的Windows PowerShell版本，提供完全相同的功能，适用于Windows开发环境。

## 🔧 系统要求

- **操作系统**: Windows 10/11 或 Windows Server 2016+
- **PowerShell**: Windows PowerShell 5.1+ 或 PowerShell Core 6.0+
- **Java**: JDK 8 或更高版本
- **Maven**: Apache Maven 3.6.3+
- **.NET Framework**: 4.5+ (对于Windows PowerShell)

## 📁 文件说明

| 文件名 | 描述 |
|--------|------|
| `build-and-test.ps1` | 主PowerShell脚本 |
| `run-build-and-test.bat` | 启动批处理文件（推荐使用） |
| `README-Windows.md` | 本使用说明文档 |

## 🚀 快速开始

### 方式一：使用批处理启动器（推荐）

```cmd
# 完整构建和测试流程
run-build-and-test.bat

# 仅构建项目
run-build-and-test.bat -BuildOnly

# 显示帮助信息
run-build-and-test.bat -Help
```

### 方式二：直接执行PowerShell脚本

```powershell
# 设置执行策略（可能需要管理员权限）
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 执行脚本
.\build-and-test.ps1

# 或者绕过执行策略
powershell.exe -ExecutionPolicy Bypass -File "build-and-test.ps1"
```

## 📖 使用示例

### 基本用法

```powershell
# 1. 完整构建和测试流程（默认）
.\build-and-test.ps1

# 2. 仅构建项目
.\build-and-test.ps1 -BuildOnly

# 3. 使用指定的Java路径
.\build-and-test.ps1 -JavaHome "C:\Program Files\Java\jdk1.8.0_331"

# 4. 构建后仅运行单元测试
.\build-and-test.ps1 -UnitOnly

# 5. 包含性能测试的完整流程
.\build-and-test.ps1 -Performance

# 6. 详细输出模式
.\build-and-test.ps1 -Verbose
```

### 使用批处理启动器

```cmd
# 所有PowerShell参数都可以通过批处理文件传递
run-build-and-test.bat -BuildOnly
run-build-and-test.bat -JavaHome "C:\Java\jdk1.8.0" -Verbose
run-build-and-test.bat -UnitOnly -NoReport
```

## ⚙️ 参数说明

### 执行模式选项
- `-BuildOnly`: 仅执行构建（编译+安装），不运行测试
- `-BuildAndTest`: 执行完整流程（构建+测试）[默认]

### Java环境选项
- `-JavaHome PATH`: 指定Java JDK路径
- `-UseDefaultJava`: 使用系统默认Java

### 测试控制选项
- `-UnitOnly`: 仅运行单元测试
- `-IntegrationOnly`: 仅运行集成测试
- `-Performance`: 包含性能测试
- `-SkipCoverage`: 跳过覆盖率报告生成

### 执行控制选项
- `-NoCleanup`: 执行前不清理环境
- `-NoReport`: 不生成最终报告
- `-Verbose`: 显示详细输出
- `-Help`: 显示帮助信息

## 🔧 环境配置

### Java环境配置

脚本会按以下优先级寻找Java：
1. `-JavaHome` 参数指定的路径
2. 默认Java 8路径：`$env:USERPROFILE\.jdks\corretto-1.8.0_452`
3. 系统环境变量 `JAVA_HOME`
4. PATH中的java命令

### Maven配置

确保Maven已正确安装并添加到PATH中：
```cmd
# 检查Maven是否可用
mvn -version
```

## 📊 输出文件

执行后会在 `logs\` 目录下生成：

| 文件类型 | 文件名格式 | 描述 |
|----------|------------|------|
| 主日志 | `build-and-test-{timestamp}.log` | 详细执行日志 |
| 最终报告 | `final-report-{timestamp}.txt` | 执行结果摘要 |
| 构建日志 | `compile-output.log`, `install-output.log` | Maven构建日志 |
| 测试日志 | `test-{module}-output.log` | 各模块测试日志 |
| 覆盖率报告 | `coverage\{module}\index.html` | JaCoCo覆盖率报告 |

## 🐛 故障排除

### 执行策略问题

如果遇到执行策略限制：

```powershell
# 查看当前执行策略
Get-ExecutionPolicy

# 设置执行策略（管理员权限）
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned

# 或者仅为当前用户设置
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 临时绕过（推荐使用批处理启动器）
powershell.exe -ExecutionPolicy Bypass -File "build-and-test.ps1"
```

### 常见错误解决

1. **"无法加载文件，因为在此系统上禁止运行脚本"**
   - 解决方案：使用批处理启动器或设置执行策略

2. **"找不到Java"**
   - 解决方案：安装Java JDK并设置JAVA_HOME，或使用-JavaHome参数

3. **"找不到Maven"**
   - 解决方案：安装Maven并确保添加到PATH中

4. **端口占用错误**
   - 解决方案：检查端口49011是否被占用，关闭占用进程

### 日志分析

查看详细错误信息：
```cmd
# 查看主日志
type logs\build-and-test-{timestamp}.log

# 查看构建错误日志
type logs\compile-error.log

# 查看测试错误日志
type logs\test-{module}-error.log
```

## 🔄 与Linux版本的差异

| 特性 | Linux (Bash) | Windows (PowerShell) |
|------|-------------|----------------------|
| 路径分隔符 | `/` | `\` |
| 环境变量 | `$HOME` | `$env:USERPROFILE` |
| 进程管理 | `kill`, `lsof` | `Stop-Process`, `Get-NetTCPConnection` |
| 文件操作 | `find`, `rm` | `Get-ChildItem`, `Remove-Item` |
| 命令执行 | `timeout` | `Start-Process -Wait` |
| 颜色输出 | ANSI转义序列 | `Write-Host -ForegroundColor` |

## 📞 技术支持

如果遇到问题：

1. 查看生成的日志文件
2. 确认系统要求已满足
3. 尝试使用批处理启动器
4. 检查PowerShell版本兼容性

## 📝 更新日志

### v2.0.0
- ✅ 完整的PowerShell版本实现
- ✅ 支持所有原始bash功能
- ✅ Windows路径和命令适配
- ✅ 批处理启动器支持
- ✅ 完整的错误处理和日志记录 